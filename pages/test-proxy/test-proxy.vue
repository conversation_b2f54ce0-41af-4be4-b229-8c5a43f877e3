<template>
  <view class="container">
    <view class="header">
      <text class="title">视频代理测试</text>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试云函数连接</text>
      <button @click="testConnection" :disabled="testing" class="test-btn">
        {{ testing ? '测试中...' : '测试连接' }}
      </button>
      <view v-if="connectionResult" class="result">
        <text :class="connectionResult.success ? 'success' : 'error'">
          {{ connectionResult.message }}
        </text>
      </view>
    </view>
    
    <view class="test-section">
      <text class="section-title">测试视频代理</text>
      <input 
        v-model="testUrl" 
        placeholder="输入微博视频URL进行测试"
        class="url-input"
      />
      <button @click="testVideoProxy" :disabled="testing || !testUrl" class="test-btn">
        {{ testing ? '测试中...' : '测试视频代理' }}
      </button>
      <view v-if="proxyResult" class="result">
        <text :class="proxyResult.success ? 'success' : 'error'">
          {{ proxyResult.message }}
        </text>
        <view v-if="proxyResult.success && proxyResult.proxyUrl" class="proxy-url">
          <text class="label">代理URL:</text>
          <text class="url">{{ proxyResult.proxyUrl }}</text>
        </view>
      </view>
    </view>
    
    <view class="config-section">
      <text class="section-title">当前配置</text>
      <view class="config-item">
        <text class="label">代理服务地址:</text>
        <text class="value">{{ config.proxy.baseUrl || '未配置' }}</text>
      </view>
      <view class="config-item">
        <text class="label">代理状态:</text>
        <text :class="config.proxy.enabled ? 'enabled' : 'disabled'">
          {{ config.proxy.enabled ? '已启用' : '已禁用' }}
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import config from '@/components/config.json'

export default {
  data() {
    return {
      config,
      testing: false,
      connectionResult: null,
      proxyResult: null,
      testUrl: 'https://video.weibocdn.com/test.mp4' // 示例URL
    }
  },
  
  methods: {
    async testConnection() {
      this.testing = true
      this.connectionResult = null
      
      try {
        if (!this.config.proxy.baseUrl) {
          throw new Error('代理服务地址未配置')
        }
        
        // 测试云函数是否可访问
        const response = await uni.request({
          url: this.config.proxy.baseUrl,
          method: 'GET',
          timeout: 10000
        })
        
        if (response.statusCode === 400) {
          // 400错误是预期的，因为没有提供url参数
          this.connectionResult = {
            success: true,
            message: '云函数连接正常！'
          }
        } else {
          this.connectionResult = {
            success: false,
            message: `意外的响应状态: ${response.statusCode}`
          }
        }
      } catch (error) {
        this.connectionResult = {
          success: false,
          message: `连接失败: ${error.message}`
        }
      } finally {
        this.testing = false
      }
    },
    
    async testVideoProxy() {
      this.testing = true
      this.proxyResult = null
      
      try {
        if (!this.config.proxy.baseUrl) {
          throw new Error('代理服务地址未配置')
        }
        
        if (!this.testUrl) {
          throw new Error('请输入测试URL')
        }
        
        // 构建代理URL
        const proxyUrl = `${this.config.proxy.baseUrl}?url=${encodeURIComponent(this.testUrl)}`
        
        // 测试代理请求
        const response = await uni.request({
          url: proxyUrl,
          method: 'GET',
          timeout: 15000
        })
        
        if (response.statusCode === 200) {
          this.proxyResult = {
            success: true,
            message: '视频代理测试成功！',
            proxyUrl
          }
        } else {
          this.proxyResult = {
            success: false,
            message: `代理请求失败: ${response.statusCode} - ${response.data}`
          }
        }
      } catch (error) {
        this.proxyResult = {
          success: false,
          message: `代理测试失败: ${error.message}`
        }
      } finally {
        this.testing = false
      }
    }
  }
}
</script>

<style scoped>
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.test-section, .config-section {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  display: block;
}

.test-btn {
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 16px;
  margin-top: 10px;
}

.test-btn:disabled {
  background-color: #ccc;
}

.url-input {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  margin-bottom: 10px;
  box-sizing: border-box;
}

.result {
  margin-top: 15px;
  padding: 10px;
  border-radius: 6px;
}

.success {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.error {
  color: #ff4d4f;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.proxy-url {
  margin-top: 10px;
  padding: 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.label {
  font-weight: bold;
  color: #666;
}

.value {
  color: #333;
  word-break: break-all;
}

.enabled {
  color: #52c41a;
  font-weight: bold;
}

.disabled {
  color: #ff4d4f;
  font-weight: bold;
}

.url {
  font-family: monospace;
  font-size: 12px;
  color: #666;
  word-break: break-all;
}
</style>
