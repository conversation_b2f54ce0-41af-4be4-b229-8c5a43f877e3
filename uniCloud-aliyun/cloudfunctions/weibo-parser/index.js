'use strict';

const fs = require('fs');
const path = require('path');

/**
 * 微博视频解析器
 * 支持微博视频链接解析和去水印处理
 */

// 全局调试配置
let DEBUG = false;

// 通用请求头
const COMMON_HEADERS = {
  'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
  'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
  'Accept-Encoding': 'gzip, deflate, br',
  'Connection': 'keep-alive',
  'Upgrade-Insecure-Requests': '1',
  'Cache-Control': 'no-cache',
  'Pragma': 'no-cache'
};

// PC端请求头 - 使用最新版本浏览器UA
const DESKTOP_HEADERS = {
  ...COMMON_HEADERS,
  'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  'Referer': 'https://weibo.com/',
  'Sec-Fetch-Dest': 'document',
  'Sec-Fetch-Mode': 'navigate',
  'Sec-Fetch-Site': 'same-origin',
  'Sec-Fetch-User': '?1',
  'Sec-Ch-Ua': '"Not A(Brand";v="99", "Google Chrome";v="121", "Chromium";v="121"',
  'Sec-Ch-Ua-Mobile': '?0',
  'Sec-Ch-Ua-Platform': '"macOS"'
};

// 已移除备用UA策略，仅保留桌面端头
// 移动端请求头（用于尝试拿 H5 直链）
const MOBILE_HEADERS = {
  ...COMMON_HEADERS,
  'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
  'Referer': 'https://m.weibo.cn/',
  'Sec-Fetch-Dest': 'document',
  'Sec-Fetch-Mode': 'navigate',
  'Sec-Fetch-Site': 'same-origin',
  'Sec-Fetch-User': '?1'
};

exports.main = async (event, context) => {
  const { link, forceRemoveWatermark = false, debug = false } = event || {};
  DEBUG = !!debug;

  if (!link || typeof link !== 'string') {
    return { success: false, message: '链接不能为空' };
  }

  try {
    if (DEBUG) console.log('开始解析微博链接:', link);
    
    // 清理链接
    const cleanedLink = cleanWeiboUrl(link);
    if (DEBUG) console.log('清理后的链接:', cleanedLink);

    // 解析微博视频
    const result = await parseWeiboVideo(cleanedLink);
    
    if (result.success) {
      if (DEBUG) console.log('解析成功:', result.data);
      return {
        success: true,
        data: result.data,
        timestamp: new Date().toISOString(),
        version: '微博解析器 v1.0'
      };
    } else {
      if (DEBUG) console.log('微博解析失败:', result.message);
      return result;
    }

  } catch (error) {
    console.error('微博解析器异常:', error);
    return {
      success: false,
      message: error.message || '解析失败',
      error: error.toString()
    };
  }
};

/**
 * 清理微博链接
 */
function cleanWeiboUrl(url) {
  if (!url) return url;
  
  // 移除多余的参数和清理链接
  let cleaned = url.trim();
  
  // 如果是短链接，保持原样
  if (cleaned.includes('weibo.com/tv/') || cleaned.includes('video.weibo.com/show')) {
    return cleaned;
  }
  
  // 移除不必要的参数
  cleaned = cleaned.replace(/[?&]from=.*?(?=[?&]|$)/, '');
  cleaned = cleaned.replace(/[?&]type=.*?(?=[?&]|$)/, '');
  
  return cleaned;
}

/**
 * 解析微博视频
 */
async function parseWeiboVideo(url) {
  try {
    // 1. 先获取重定向后的真实URL
    const realUrl = await getRealUrl(url);
    if (DEBUG) console.log('获取到真实URL:', realUrl);
    
    // 2. 仅使用 TV 组件接口（唯一策略）
    if (DEBUG) console.log('尝试TV组件接口解析');
      const tvApiResult = await tryParseViaTvComponent(realUrl);
      if (tvApiResult.success) {
        return tvApiResult;
    }

    if (DEBUG) console.log('微博解析失败: 仅TV接口未返回有效数据');
    throw new Error(tvApiResult.message || '无法解析微博内容，请稍后重试');
    
  } catch (error) {
    throw error;
  }
}

/**
 * 获取重定向后的真实URL
 */
async function getRealUrl(shareUrl) {
  try {
    const response = await uniCloud.httpclient.request(shareUrl, {
      method: 'GET',
      followRedirect: false,  // 不自动跟随重定向
      timeout: 15000,
      headers: DESKTOP_HEADERS
    });

    const location = response.headers.location || response.headers.Location;
    const realUrl = location || shareUrl;
    
    if (DEBUG) console.log('获取真实URL:', realUrl);
    return realUrl;
  } catch (error) {
    console.error('获取真实URL失败:', error);
    return shareUrl;
  }
}

function extractWeiboIdFromUrl(url) {
  if (!url) return '';
  
  console.log('提取微博ID，原始URL:', url);
  
  // 匹配 1034:xxxxxxxx (保持完整ID)
  const fidMatch = url.match(/1034:(\d{13,})/);
  if (fidMatch) {
    console.log('提取到fid格式ID(仅数字部分):', fidMatch[1]);
    return fidMatch[1];
  }
  
  // 匹配 /detail/xxxxxxxx 或 /status/xxxxxxxx
  const mDetail = url.match(/\/(?:detail|status)\/(\d{13,})/);
  if (mDetail) {
    console.log('提取到detail格式ID:', mDetail[1]);
    return mDetail[1];
  }
  
  // 尝试从URL路径中提取其他格式的ID
  const generalMatch = url.match(/\/(\d{13,})/);
  if (generalMatch) {
    console.log('提取到通用格式ID:', generalMatch[1]);
    return generalMatch[1];
  }
  
  console.log('未能提取到有效ID');
  return '';
}

// 提取完整的 OID（形如 1034:xxxxxxxx），若只提取到纯数字则补全前缀
function extractFullOidFromUrl(url) {
  if (!url) return '';
  const fullMatch = url.match(/1034:(\d{13,})/);
  if (fullMatch) {
    return `1034:${fullMatch[1]}`;
  }
  const idOnly = extractWeiboIdFromUrl(url);
  if (idOnly) {
    return `1034:${idOnly}`;
  }
  return '';
}

// 保留占位（不再使用）
function collectVideoCandidates() { return []; }

function stripHtml(html) { return String(html || '').replace(/<[^>]+>/g, ' ').replace(/\s+/g, ' ').trim(); }

// 移除页面抓取与备用解析路径，专注 TV 接口

/**
 * 构建移动端详情页链接
 */
function buildMobileDetailUrl(url) { return url; }

/**
 * 通过TV组件接口尝试解析（基于PHP版本优化）
 */
async function tryParseViaTvComponent(url) {
  try {
    if (DEBUG) console.log(`🎯 尝试通过TV组件API解析: ${url}`);
    
    // 从URL中提取完整OID（优先 1034:xxxxxxxx）
    const fullOid = extractFullOidFromUrl(url);
    if (!fullOid) {
      throw new Error('无法从URL中提取微博ID');
    }
    const weiboId = fullOid.includes(':') ? fullOid.split(':')[1] : fullOid;
    
    const postData = `data=${encodeURIComponent(JSON.stringify({
      "Component_Play_Playinfo": {
        "oid": fullOid
      }
    }))}`;

    const tvHeaders = {
      ...DESKTOP_HEADERS,
      'Accept': 'application/json, text/plain, */*',
      'Content-Type': 'application/x-www-form-urlencoded',
      'Referer': `https://weibo.com/tv/show/${fullOid}`,
      'X-Requested-With': 'XMLHttpRequest',
      'Origin': 'https://weibo.com',
      'Cookie': generateGuestCookie()
    };

    // 附带 page 参数（与成功的 PHP 版本一致）
    const requestUrl = `https://weibo.com/tv/api/component?page=${encodeURIComponent(`/tv/show/${fullOid}`)}`;

    if (DEBUG) console.log('使用移动端请求头获取TV API数据');
    if (DEBUG) console.log('TV API请求URL:', requestUrl);
    if (DEBUG) console.log('TV API POST数据:', postData);

    const response = await uniCloud.httpclient.request(
      requestUrl,
      {
        method: 'POST',
        headers: tvHeaders,
        data: postData,
    timeout: 15000,
    dataType: 'json',
    followRedirect: true,
    maxRedirects: 3
      }
    );

    if (response.status !== 200) {
      throw new Error(`TV API状态码异常: ${response.status}`);
    }

    const responseData = response.data;
    if (DEBUG) {
      // 仅打印简要信息，避免输出完整JSON
      try {
        const brief = JSON.stringify(responseData).substring(0, 300) + '...';
        console.log('TV API响应(简要):', brief);
      } catch (_) {}
    }
    // 将原始JSON保存到函数目录，便于排查（本地调试可写，云端可能受限）
    try {
      const dumpPath = path.join(__dirname, 'weibo-api-response.json');
      fs.writeFileSync(dumpPath, JSON.stringify(responseData, null, 2), 'utf8');
      if (DEBUG) console.log('已写入原始JSON到文件:', dumpPath);
    } catch (e) {
      if (DEBUG) console.log('写入原始JSON文件失败:', e.message);
    }

    if (responseData && responseData.code === "100000" && responseData.data && responseData.data.Component_Play_Playinfo) {
      const playInfo = responseData.data.Component_Play_Playinfo;
      if (DEBUG) console.log('成功获取播放信息:', Object.keys(playInfo));

      let videoUrl = '';
      const qualityUrls = {};
      const allVideoUrls = [];

      if (playInfo.urls && typeof playInfo.urls === 'object') {
        for (const [quality, url] of Object.entries(playInfo.urls)) {
          const fullUrl = typeof url === 'string' ? (url.startsWith('//') ? `https:${url}` : url) : '';
          if (!fullUrl) continue;
          qualityUrls[quality] = fullUrl;
          allVideoUrls.push(fullUrl);
          if (DEBUG) console.log(`${quality}视频URL:`, fullUrl.substring(0, 120) + '...');
        }

        const qualityPriority = ['高清 1080P', '高清 720P', '清晰 480P', '流畅 360P'];
        for (const quality of qualityPriority) {
          if (qualityUrls[quality]) {
            videoUrl = qualityUrls[quality];
            break;
          }
        }

        if (!videoUrl && allVideoUrls.length > 0) {
          videoUrl = allVideoUrls[0];
        }
      }

      // 从 playInfo 递归收集所有可能的 mp4 直链，补充候选
      try {
        const extraUrls = collectMp4UrlsFromObject(playInfo);
        for (const u of extraUrls) {
          if (!allVideoUrls.includes(u)) allVideoUrls.push(u);
        }
      } catch (e) {
        if (DEBUG) console.log('收集额外URL失败:', e.message);
      }

      // 为 H5 环境按优先级选择最友好的直链（sinaimg.cn + label 包含 h5 优先）
      if (allVideoUrls.length > 0) {
        const ranked = rankWeiboVideoUrlsForH5(allVideoUrls);
        if (ranked.length > 0) {
          videoUrl = ranked[0];
        }
      }

      if (videoUrl) {
        // 若非 H5 友好直链，尝试以移动端 UA + m.weibo.cn Referer 再请求一次，获取 H5 直链
        let isH5Friendly = /sinaimg\.cn/i.test(videoUrl) || /label=([^&]*h5[^&]*)/i.test(videoUrl);
        if (!isH5Friendly) {
          // 先尝试 PC AJAX statuses/show 接口
          try {
            const ajaxH5Candidates = await tryFetchH5UrlsViaPcAjax(weiboId, fullOid);
            if (ajaxH5Candidates && ajaxH5Candidates.length > 0) {
              const ranked = rankWeiboVideoUrlsForH5(ajaxH5Candidates);
              if (ranked.length > 0) {
                videoUrl = ranked[0];
                for (const u of ranked) {
                  if (!allVideoUrls.includes(u)) allVideoUrls.push(u);
                }
                isH5Friendly = /sinaimg\.cn/i.test(videoUrl) || /label=([^&]*h5[^&]*)/i.test(videoUrl);
              }
            }
          } catch (e) {
            if (DEBUG) console.log('PC AJAX statuses/show 兜底失败:', e.message);
          }

          // 再尝试移动端 TV 请求
          try {
            const mobileHeaders = {
              ...MOBILE_HEADERS,
              'Accept': 'application/json, text/plain, */*',
              'Content-Type': 'application/x-www-form-urlencoded',
              'Referer': `https://m.weibo.cn/detail/${weiboId}`,
              'Origin': 'https://m.weibo.cn',
              'Cookie': generateGuestCookie()
            };
            const mobileResp = await uniCloud.httpclient.request(
              requestUrl,
              {
                method: 'POST',
                headers: mobileHeaders,
                data: postData,
                timeout: 15000,
                dataType: 'json',
                followRedirect: true,
                maxRedirects: 3
              }
            );
            if (mobileResp.status === 200 && mobileResp.data) {
              const mData = mobileResp.data;
              const mInfo = mData?.data?.Component_Play_Playinfo || null;
              if (mInfo) {
                const mAll = [];
                if (mInfo.urls && typeof mInfo.urls === 'object') {
                  for (const u of Object.values(mInfo.urls)) {
                    if (typeof u === 'string') {
                      const full = u.startsWith('//') ? `https:${u}` : u;
                      mAll.push(full);
                    }
                  }
                }
                try {
                  const extra = collectMp4UrlsFromObject(mInfo);
                  for (const u of extra) mAll.push(u);
                } catch (_) {}
                const ranked = rankWeiboVideoUrlsForH5(mAll);
                if (ranked.length > 0 && (/sinaimg\.cn/i.test(ranked[0]) || /label=([^&]*h5[^&]*)/i.test(ranked[0]))) {
                  videoUrl = ranked[0];
                  // 追加进候选列表方便调试
                  for (const u of ranked) {
                    if (!allVideoUrls.includes(u)) allVideoUrls.push(u);
                  }
                  isH5Friendly = true;
                }
              }
            }
          } catch (e) {
            if (DEBUG) console.log('移动端兜底获取H5直链失败:', e.message);
          }

          // 再次兜底：直接抓取 m.weibo.cn 详情页，解析页面脚本中的 mp4 链接
          if (!isH5Friendly) {
            try {
              const h5Candidates = await tryFetchH5UrlsViaMobilePage(weiboId);
              if (h5Candidates && h5Candidates.length > 0) {
                const ranked = rankWeiboVideoUrlsForH5(h5Candidates);
                if (ranked.length > 0) {
                  videoUrl = ranked[0];
                  for (const u of ranked) {
                    if (!allVideoUrls.includes(u)) allVideoUrls.push(u);
                  }
                  isH5Friendly = /sinaimg\.cn/i.test(videoUrl) || /label=([^&]*h5[^&]*)/i.test(videoUrl);
                }
              }
            } catch (e) {
              if (DEBUG) console.log('解析 m.weibo.cn 详情页失败:', e.message);
            }
          }
        }

        if (DEBUG) console.log('TV组件接口解析成功，视频URL:', videoUrl);

        const coverUrl = playInfo.cover_image ? (playInfo.cover_image.startsWith('//') ? `https:${playInfo.cover_image}` : playInfo.cover_image) : '';
        const avatarUrl = playInfo.avatar ? (playInfo.avatar.startsWith('//') ? `https:${playInfo.avatar}` : playInfo.avatar) : '';

        let content = '';
        if (playInfo.text) {
          content = playInfo.text;
        } else if (playInfo.content) {
          content = playInfo.content;
        } else if (playInfo.description) {
          content = playInfo.description;
        } else if (playInfo.summary) {
          content = playInfo.summary;
        } else if (playInfo.title) {
          content = playInfo.title;
        }

        const requiresProxy = /weibocdn\.com/i.test(videoUrl) && !isH5Friendly;

        return {
          success: true,
          data: {
            title: playInfo.title || '微博视频',
            author: playInfo.author || '未知用户',
            videoUrl: videoUrl,
            coverUrl: coverUrl,
            content: content || '暂无描述',
            hasVideo: true,
            selectedQuality: videoUrl ? Object.keys(qualityUrls).find(q => qualityUrls[q] === videoUrl) || '未知' : '未知',
            qualityUrls: qualityUrls,
            videoUrls: allVideoUrls,
            method: 'tv_component_api',
            authorId: playInfo.author_id || '',
            playCount: playInfo.play_count || 0,
            date: playInfo.date || '',
            avatar: avatarUrl,
            notice: isH5Friendly ? '已返回H5友好直链，可直接播放' : '已返回直链，如浏览器403将自动走代理',
            isH5Friendly,
            requiresProxy
          }
        };
      }

      // 即使没有拿到直链，也返回基础信息，先让前端可展示标题/作者
      const coverUrl = playInfo.cover_image ? (playInfo.cover_image.startsWith('//') ? `https:${playInfo.cover_image}` : playInfo.cover_image) : '';
      const avatarUrl = playInfo.avatar ? (playInfo.avatar.startsWith('//') ? `https:${playInfo.avatar}` : playInfo.avatar) : '';
      let content = '';
      if (playInfo.text) content = playInfo.text;
      else if (playInfo.content) content = playInfo.content;
      else if (playInfo.description) content = playInfo.description;
      else if (playInfo.summary) content = playInfo.summary;
      else if (playInfo.title) content = playInfo.title;

      return {
        success: true,
        data: {
          title: playInfo.title || '微博视频',
          author: playInfo.author || '未知用户',
          videoUrl: '',
          coverUrl: coverUrl,
          content: content || '暂无描述',
          hasVideo: false,
          selectedQuality: '未知',
          qualityUrls: qualityUrls,
          videoUrls: allVideoUrls,
          method: 'tv_component_api',
          authorId: playInfo.author_id || '',
          playCount: playInfo.play_count || 0,
          date: playInfo.date || '',
          avatar: avatarUrl,
          notice: '未获取到直链，已返回基础信息（标题/作者）'
        }
      };
    }

    if (DEBUG) console.log('❌ TV组件API中未找到视频信息');
    return { success: false, message: 'TV组件API中未找到视频信息' };

  } catch (error) {
    if (DEBUG) console.log('❌ TV组件API解析异常:', error.message);
    return { success: false, message: error.message };
  }
}

/**
 * 生成访客Cookie，模拟真实浏览器访问
 */
function generateGuestCookie() {
  // 使用稳定的访客Cookie，确保返回的签名URL可直接访问
  return 'SUB=_2AkMfFmU_f8NxqwFRmvkWzGrqbIt2zA3EieKpSpTkJRMxHRl-yT9kqmE8tRB6NJZL0M6C_1oEOnh9yqIOVA80rQqt6NcX; SUBP=0033WrSXqPxfM72-Ws9jqgMF55529P9D9W5zakNEPhSYpAFjGDJ2wn0W; _s_tentry=passport.weibo.com; Apache=7094693300415.298.1749740041909; SINAGLOBAL=7094693300415.298.1749740041909; ULV=1749740041974:1:1:1:7094693300415.298.1749740041909:; WBPSESS=LKUy5Npwn5zVNZX-hrYAMCtgRKCpBOAQYN_DEoweut6xx3CBGNvnlySn4uBpFMEMoXOJXLPRmC47nWOg5psVsQ0OJg7B8KpobiCcLMAMnDzAvoAw7SpGuWfsmTBg5Pq7';
}

/**
 * 增强请求头，添加随机化和访客Cookie
 */
function enhanceHeaders(baseHeaders) { return { ...baseHeaders, Cookie: generateGuestCookie() }; }

/**
 * 从TV API数据中提取视频信息
 */
function extractTvApiVideoInfo(data) {
  try {
    let title = '';
    let author = '';
    let videoUrl = '';
    let coverUrl = '';
    let content = '';
    
    // 尝试从不同字段提取信息
    if (data.video) {
      const video = data.video;
      title = video.title || video.name || '';
      author = video.author || (video.user && video.user.screen_name) || '';
      videoUrl = video.play_info?.mp4_url || video.video_url || video.url || '';
      coverUrl = video.pic || video.cover || video.poster || '';
      content = video.description || video.summary || '';
    }
    
    // 如果没有从video字段获取到，尝试其他字段
    if (!videoUrl && data.media_info) {
      const media = data.media_info;
      videoUrl = media.stream_url || media.mp4_hd_url || media.mp4_ld_url || '';
      title = title || media.title || '';
      coverUrl = coverUrl || media.poster || '';
    }
    
    // 从页面信息获取
    if (data.page_info) {
      const page = data.page_info;
      title = title || page.title || '';
      author = author || (page.user && page.user.screen_name) || '';
      if (page.media_info) {
        videoUrl = videoUrl || page.media_info.stream_url || page.media_info.mp4_hd_url || '';
        coverUrl = coverUrl || page.media_info.poster || '';
      }
    }
    
    return {
      title: title || '微博视频',
      author: author || '未知用户',
      videoUrl: videoUrl || '',
      coverUrl: coverUrl || '',
      content: content || '',
      hasVideo: !!videoUrl
    };
    
  } catch (error) {
    console.error('提取TV API视频信息失败:', error);
    return {
      title: '微博视频',
      author: '未知用户',
      videoUrl: '',
      coverUrl: '',
      content: '',
      hasVideo: false
    };
  }
}

// 已删除国际版API方法 - 专注于TV组件接口

/**
 * 延迟函数
 */
function delay(ms) { return new Promise(resolve => setTimeout(resolve, ms)); }

/**
 * 使用随机UA重试解析
 */
async function tryParseWithRandomUA() { throw new Error('disabled'); }

/**
 * 尝试直接构造视频链接（基于已知的微博CDN格式）
 */
async function tryDirectVideoUrlConstruction() { throw new Error('disabled'); }

/**
 * 检查视频URL是否有效
 */
async function checkVideoUrlValid(videoUrl) {
  try {
    const response = await uniCloud.httpclient.request(videoUrl, {
      method: 'HEAD',
      headers: {
        ...COMMON_HEADERS,
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1'
      },
      timeout: 10000,
      followRedirect: true,
      maxRedirects: 3
    });
    
    const contentType = response.headers['content-type'] || '';
    const contentLength = parseInt(response.headers['content-length'] || '0');
    
    // 检查是否是视频文件
    const isVideo = contentType.includes('video/') || 
                   contentType.includes('application/octet-stream') ||
                   contentType.includes('binary/octet-stream');
    const isNotHtml = !contentType.includes('text/html') && !contentType.includes('text/plain');
    const hasValidSize = contentLength > 100000; // 至少100KB，视频文件应该比较大
    
    console.log(`URL检查结果: ${videoUrl} - 状态:${response.status}, 类型:${contentType}, 大小:${contentLength}`);
    
    // 更严格的检查：必须是视频类型且不是HTML，或者是大文件且不是HTML
    const isValidVideo = response.status === 200 && (
      (isVideo && isNotHtml) || 
      (hasValidSize && isNotHtml)
    );
    
    return {
      valid: isValidVideo,
      contentType,
      contentLength
    };
    
  } catch (error) {
    console.log('URL检查失败:', error.message);
    return { valid: false };
  }
}

/**
 * 从页面脚本中提取视频信息
 */
async function extractVideoFromPageScript() { throw new Error('disabled'); }

/**
 * 从对象中递归收集可能的 mp4 直链
 */
function collectMp4UrlsFromObject(obj) {
  const collected = new Set();
  const stack = [obj];
  while (stack.length > 0) {
    const cur = stack.pop();
    if (!cur || typeof cur !== 'object') continue;
    for (const key of Object.keys(cur)) {
      const val = cur[key];
      if (typeof val === 'string') {
        const s = val.startsWith('//') ? `https:${val}` : val;
        if (/^https?:\/\//i.test(s) && /\.mp4(\?|$)/i.test(s)) {
          collected.add(s);
        }
      } else if (val && typeof val === 'object') {
        stack.push(val);
      }
    }
  }
  return Array.from(collected);
}

/**
 * 为 H5 播放进行 URL 评分排序
 * - 优先域名含 sinaimg.cn
 * - 优先 label 含 h5/vertical_video_h5
 * - 其次 template/路径含 1080/720
 * - 尽量避免 weibocdn.com
 */
function rankWeiboVideoUrlsForH5(urls) {
  return urls
    .map((url) => {
      let score = 0;
      const u = url.toLowerCase();
      if (u.includes('sinaimg.cn')) score += 100;
      const labelMatch = u.match(/[?&]label=([^&]+)/);
      if (labelMatch) {
        const label = decodeURIComponent(labelMatch[1]);
        if (/h5/.test(label) || /vertical_video_h5/.test(label)) score += 60;
      }
      const templateMatch = u.match(/[?&]template=([^&]+)/);
      if (templateMatch) {
        const tpl = decodeURIComponent(templateMatch[1]);
        if (/1080/.test(tpl)) score += 20;
        if (/720/.test(tpl)) score += 10;
      }
      if (/1080/.test(u)) score += 10;
      if (/720/.test(u)) score += 5;
      if (u.includes('weibocdn.com')) score -= 20;
      return { url, score };
    })
    .sort((a, b) => b.score - a.score)
    .map(item => item.url);
}

/**
 * 直接抓取移动端详情页，尝试从页面中提取 mp4 直链（H5可播）
 */
async function tryFetchH5UrlsViaMobilePage(weiboId) {
  const detailUrl = `https://m.weibo.cn/detail/${weiboId}`;
  const headers = {
    ...MOBILE_HEADERS,
    'Referer': 'https://m.weibo.cn/',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8'
  };
  const res = await uniCloud.httpclient.request(detailUrl, {
    method: 'GET',
    headers,
    timeout: 15000,
    dataType: 'text',
    followRedirect: true,
    maxRedirects: 3
  });
  const html = res.data || '';
  // 简单从 HTML 里提取可能的 mp4 链接
  const urls = [];
  const mp4Regex = /https?:\/\/[^"'\s]+\.mp4[^"'\s]*/ig;
  let m;
  while ((m = mp4Regex.exec(html)) !== null) {
    const u = m[0];
    // 过滤掉明显的 weibo 静态资源脚本
    if (/weibo\.cn\//i.test(u)) continue;
    urls.push(u);
  }
  return Array.from(new Set(urls));
}

/**
 * 尝试通过 PC 端 AJAX 接口获取 H5 直链
 * 逻辑：模拟 PC UA + weibo.com Referer，访问 /ajax/statuses/show?id=OID 或 /ajax/statuses/mshow?id=OID
 */
async function tryFetchH5UrlsViaPcAjax(weiboId, fullOid) {
  // OID 形如 1034:xxxxxxxx
  const oid = fullOid;
  const ajaxUrls = [
    `https://weibo.com/ajax/statuses/show?id=${encodeURIComponent(oid)}`,
    `https://weibo.com/ajax/statuses/mshow?id=${encodeURIComponent(oid)}`
  ];
  const headers = {
    ...DESKTOP_HEADERS,
    'Accept': 'application/json, text/plain, */*',
    'X-Requested-With': 'XMLHttpRequest',
    'Cookie': generateGuestCookie()
  };
  const candidates = [];
  for (const url of ajaxUrls) {
    try {
      const resp = await uniCloud.httpclient.request(url, {
        method: 'GET',
        headers,
        dataType: 'json',
        timeout: 12000,
        followRedirect: true,
        maxRedirects: 3
      });
      if (resp.status === 200 && resp.data) {
        const extra = collectMp4UrlsFromObject(resp.data);
        for (const u of extra) candidates.push(u);
      }
    } catch (e) {
      if (DEBUG) console.log('PC AJAX 调用失败:', e.message);
    }
  }
  return Array.from(new Set(candidates));
}


