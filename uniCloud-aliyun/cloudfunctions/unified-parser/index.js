'use strict';

/**
 * 统一解析器入口 v1.0.0
 * 支持多平台内容解析的统一接口
 * 
 * 支持的平台：
 * - 抖音 (douyin.com, dy.com)
 * - 快手 (kuaishou.com, ks.com, v.kuaishou.com, kwai.app, ks.app, gifshow.com)
 * - 小红书 (xiaohongshu.com, xhslink.com)
 * - B站 (bilibili.com, b23.tv, b23.com)
 * - 微博 (video.weibo.com, weibo.com/tv, m.weibo.cn)
 */

// 平台配置 - 配置化管理，便于扩展
const PLATFORM_CONFIG = {
  douyin: {
    name: '抖音',
    parser: 'simple-douyin-parser',
    patterns: [
      /douyin\.com/i,
      /dy\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: true,
      debug: false
    }
  },
  kuaishou: {
    name: '快手', 
    parser: 'simple-kuaishou-parser',
    patterns: [
      /kuaishou\.com/i,
      /ks\.com/i,
      /v\.kuaishou\.com/i,
      /kwai\.app/i,
      /ks\.app/i,
      /gifshow\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: true,
      debug: false
    }
  },
  xiaohongshu: {
    name: '小红书',
    parser: 'xiaohongshu-parser',
    patterns: [
      /xiaohongshu\.com/i,
      /xhslink\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: true
    }
  },
  bilibili: {
    name: 'B站',
    parser: 'bilibili-parser',
    patterns: [
      /bilibili\.com/i,
      /b23\.tv/i,
      /b23\.com/i
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: false
    }
  },
  weibo: {
    name: '微博',
    parser: 'weibo-parser',
    patterns: [
      /video\.weibo\.com/i,
      /weibo\.com\/tv/i,
      /m\.weibo\.cn/i,
      /weibo\.com\/[^\/]+\/status/i
    ],
    defaultOptions: {
      forceRemoveWatermark: false,
      debug: false
    }
  }
};

/**
 * 验证链接有效性
 * @param {string} link - 分享链接
 * @returns {boolean} - 是否有效
 */
function isValidLink(link) {
  if (!link || typeof link !== 'string') {
    return false;
  }
  
  // 使用所有平台的正则表达式进行验证
  for (const [platform, config] of Object.entries(PLATFORM_CONFIG)) {
    if (config.patterns.some(pattern => pattern.test(link))) {
      return true;
    }
  }
  
  return false;
}

/**
 * 检测平台类型
 * @param {string} link - 分享链接
 * @returns {string|null} - 平台标识或null
 */
function detectPlatform(link) {
  if (!link || typeof link !== 'string') {
    return null;
  }
  
  for (const [platform, config] of Object.entries(PLATFORM_CONFIG)) {
    if (config.patterns.some(pattern => pattern.test(link))) {
      return platform;
    }
  }
  
  return null;
}

/**
 * 清理链接 - 提取主要URL
 * @param {string} text - 包含链接的文本
 * @returns {string} - 清理后的链接
 */
function cleanLink(text) {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  // 移除多余的空格和换行
  text = text.trim().replace(/\s+/g, ' ');
  
  // 提取URL部分
  const urlMatch = text.match(/(https?:\/\/[^\s]+)/);
  if (urlMatch) {
    return urlMatch[1];
  }
  
  // 如果没有找到完整URL，尝试提取域名相关部分
  const domainMatch = text.match(/([a-zA-Z0-9.-]+\.(com|cn|net|org)\/[^\s]*)/);
  if (domainMatch) {
    return 'https://' + domainMatch[1];
  }
  
  return text.trim();
}

/**
 * 标准化返回结果 - 确保各平台返回格式统一
 * @param {Object} result - 解析器返回的结果
 * @param {string} platform - 平台标识
 * @param {string} originalLink - 原始链接
 * @returns {Object} - 标准化后的结果
 */
function standardizeResult(result, platform, originalLink) {
  console.log('standardizeResult 输入:', { 
    hasResult: !!result, 
    hasResultResult: !!result?.result,
    hasResultResultSuccess: !!result?.result?.success,
    hasResultResultData: !!result?.result?.data,
    platform, 
    originalLink 
  });

  // 处理云函数调用失败的情况
  if (!result) {
    throw new Error(`${PLATFORM_CONFIG[platform]?.name || '未知平台'}解析器无响应`);
  }

  // 处理不同的返回格式
  let data = null;
  
  // 快手解析器直接返回数据对象，包装在 {success: true, data: {...}} 中
  if (result.result && result.result.success && result.result.data) {
    data = result.result.data;
  }
  // 其他解析器可能直接返回结果对象
  else if (result.result && !result.result.success && !result.result.data) {
    // 直接返回的结果对象 (小红书、抖音)
    data = result.result;
  }
  // 处理错误情况
  else {
    const errorMsg = result.result?.message || 
                    result.result?.error?.message || 
                    `${PLATFORM_CONFIG[platform]?.name || '未知平台'}解析失败`;
    throw new Error(errorMsg);
  }
  
  const platformName = PLATFORM_CONFIG[platform]?.name || '未知平台';
  
  // 标准化字段，确保结果页面需要的字段都存在
  const standardizedResult = {
    // 基本信息
    title: data.title || '未知标题',
    author: data.author || '未知作者', 
    content: data.content || '',
    
    // 媒体内容
    processedData: data.processedData || null,
    
    // 类型和平台信息
    type: data.type || 'unknown',
    platform: platform,
    source: platformName,
    
    // 链接信息
    originalUrl: originalLink,
    originalLink: originalLink, // 保持兼容性
    
    // 附加信息
    coverUrl: data.coverUrl || null,
    note: data.note || null,
    timestamp: Date.now(),
    version: "统一解析器 v1.0.0",
    
    // 调试信息 (可选)
    debug: data.debug || null
  };

  // 若下游未提供 processedData，这里根据常见字段自动构造（优先适配微博 H5 直链）
  if (!standardizedResult.processedData) {
    const videoUrl = data.videoUrl || null;
    const videoUrls = Array.isArray(data.videoUrls) ? data.videoUrls : null;
    if (videoUrl) {
      standardizedResult.processedData = {
        isUrl: true,
        data: videoUrl,
        type: 'video/mp4',
        duration: data.duration || 0,
        isLongVideo: !!data.isLongVideo,
        qualityUrls: data.qualityUrls || null,
        videoUrls: videoUrls || null,
        isH5Friendly: !!data.isH5Friendly,
        requiresProxy: !!data.requiresProxy
      };
      standardizedResult.type = 'video';
    } else if (videoUrls && videoUrls.length > 0) {
      standardizedResult.processedData = {
        isUrl: true,
        data: videoUrls[0],
        type: 'video/mp4',
        videoUrls,
        isH5Friendly: !!data.isH5Friendly,
        requiresProxy: !!data.requiresProxy
      };
      standardizedResult.type = 'video';
    }
  }

  console.log('标准化结果:', {
    title: standardizedResult.title,
    platform: standardizedResult.platform,
    type: standardizedResult.type,
    hasProcessedData: !!standardizedResult.processedData
  });
  
  return standardizedResult;
}

/**
 * 云函数入口
 */
exports.main = async (event, context) => {
  const startTime = Date.now();
  console.log('=== unified-parser 开始处理 ===');
  console.log('请求参数:', {
    link: event.link,
    options: event.options,
    timestamp: new Date().toISOString()
  });
  
  try {
    // 参数验证
    if (!event.link) {
      throw new Error('缺少必要参数: link');
    }
    
    // 清理链接
    const cleanedLink = cleanLink(event.link);
    if (!cleanedLink) {
      throw new Error('链接格式无效');
    }
    
    console.log('清理后的链接:', cleanedLink);
    
    // 验证链接有效性
    if (!isValidLink(cleanedLink)) {
      throw new Error('不支持的链接格式，请检查链接是否正确');
    }
    
    // 检测平台
    const platform = detectPlatform(cleanedLink);
    if (!platform) {
      throw new Error('不支持的平台，当前支持：抖音、快手、小红书、B站、微博');
    }
    
    const platformConfig = PLATFORM_CONFIG[platform];
    console.log('检测到平台:', platform, '-> 解析器:', platformConfig.parser);
    
    // 准备调用参数 - 合并默认配置和用户选项
    const callParams = {
      link: cleanedLink,
      ...platformConfig.defaultOptions,
      ...(event.options || {})
    };
    
    console.log('调用解析器参数:', callParams);
    
    // 调用对应的平台解析器
    const result = await uniCloud.callFunction({
      name: platformConfig.parser,
      data: callParams
    });
    
    const endTime = Date.now();
    console.log('解析器调用完成:', {
      platform: platform,
      parser: platformConfig.parser,
      duration: endTime - startTime + 'ms',
      hasResult: !!result,
      hasResultResult: !!result?.result
    });
    
    // 标准化返回结果
    const standardizedResult = standardizeResult(result, platform, event.link);
    
    console.log('=== unified-parser 处理完成 ===');
    return standardizedResult;
    
  } catch (error) {
    const endTime = Date.now();
    console.error('=== unified-parser 处理失败 ===');
    console.error('错误详情:', {
      message: error.message,
      duration: endTime - startTime + 'ms',
      stack: error.stack
    });
    
    // 统一错误返回格式
    return {
      title: '解析失败',
      author: '系统提示', 
      content: error.message,
      processedData: null,
      type: 'error',
      platform: 'error',
      source: '系统错误',
      originalUrl: event.link || '',
      originalLink: event.link || '',
      timestamp: Date.now(),
      version: "统一解析器 v1.0.0",
      error: {
        message: error.message,
        code: 'PARSE_ERROR',
        timestamp: new Date().toISOString()
      }
    };
  }
};