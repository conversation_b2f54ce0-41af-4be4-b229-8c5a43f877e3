'use strict';

// 简单视频代理：补齐 UA/Referer/<PERSON>ie，支持 Range，白名单仅允许 weibo 视频域名

const ALLOWED_HOSTS = [
  'video.weibocdn.com',
  'fb.video.weibocdn.com',
  'f.video.weibocdn.com',
  'us.sinaimg.cn',
  'g.us.sinaimg.cn'
];

function buildHeaders(incoming, extra = {}) {
  const headers = {
    'User-Agent': incoming.headers['user-agent'] || 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1',
    'Accept': incoming.headers['accept'] || '*/*',
    'Accept-Encoding': 'identity',
    'Connection': 'keep-alive',
    ...extra
  };
  if (incoming.headers['range']) headers['Range'] = incoming.headers['range'];
  return headers;
}

function isAllowed(urlStr) {
  try {
    const u = new URL(urlStr);
    return ALLOWED_HOSTS.some(h => u.hostname.endsWith(h));
  } catch (_) {
    return false;
  }
}

exports.main = async (event, context) => {
  try {
    const httpMethod = event.httpMethod || event.method || 'GET';
    const query = event.queryStringParameters || event.query || {};
    const url = query.url || (event.body && event.body.url);
    if (!url || !isAllowed(url)) {
      return { statusCode: 400, body: 'invalid url' };
    }

    const referer = query.referer || 'https://weibo.com/';
    const headers = buildHeaders(event, {
      Referer: referer,
      Cookie: (event.headers && event.headers['x-proxy-cookie']) || ''
    });

    const upstream = await uniCloud.httpclient.request(url, {
      method: httpMethod,
      headers,
      dataType: 'stream',
      followRedirect: true,
      maxRedirects: 3,
      timeout: 30000
    });

    const respHeaders = upstream.headers || {};
    // 透传必要头
    const outHeaders = {
      'Content-Type': respHeaders['content-type'] || 'application/octet-stream',
      'Content-Length': respHeaders['content-length'] || undefined,
      'Accept-Ranges': respHeaders['accept-ranges'] || 'bytes',
      'Content-Range': respHeaders['content-range'] || undefined,
      'Cache-Control': 'no-store'
    };

    return {
      isBase64Encoded: false,
      statusCode: upstream.status,
      headers: outHeaders,
      body: upstream.data
    };
  } catch (e) {
    return { statusCode: 500, body: e.message };
  }
};



