# CLAUDE.md

此文件为 Claude Code (claude.ai/code) 在此代码库中工作时提供指导。

## 项目概述

**MarkEraser** 是一个基于 uni-app 和 uniCloud 开发的视频去水印工具。支持解析抖音、快手、小红书、B站等平台的分享链接，提取无水印的内容。

### 核心架构
- **前端**: uni-app + Vue 3，支持多端（微信小程序、H5、App）
- **后端**: uniCloud（阿里云版）+ 云函数处理内容
- **主要功能**: 链接解析、本地文件处理、历史记录、用户中心

## 核心组件

### 主要页面结构
- `pages/watermark-remover/index.vue` - 去水印主界面
- `pages/history/index.vue` - 历史记录页面
- `pages/profile/index.vue` - 用户中心和设置
- `pages/result/index.vue` - 处理结果展示
- `pages/test/` - 各平台测试页面

### 云函数 (`uniCloud-aliyun/cloudfunctions/`)

#### 核心解析系统
- `unified-parser/` - **统一解析器入口**，自动检测平台并路由到对应解析器
- `simple-douyin-parser/` - 抖音内容解析器
- `xiaohongshu-parser/` - 小红书内容专用解析器  
- `simple-kuaishou-parser/` - 快手内容解析器
- `bilibili-parser/` - B站视频解析器
- `watermark-remover/` - 通用文件处理逻辑，支持本地文件去水印

#### 系统功能模块
- `user-center/` - 用户中心和身份管理
- `uni-admin/` - 后台管理系统
- `secure-network/` - 安全网络访问控制

### 配置文件
- `pages.json` - 页面路由和底部导航配置（3个主要标签：去水印、历史、我的）
- `manifest.json` - 应用配置，微信小程序 appid: wxb29e961aeb461493
- `App.vue` - 全局应用初始化和升级检查

## 开发流程

### 运行项目
```bash
# package.json 中没有定义 npm 脚本
# 开发通过 HBuilderX IDE 进行：
# 1. 在 HBuilderX 中打开项目
# 2. 配置 uniCloud（阿里云版本）
# 3. 上传云函数
# 4. 运行到微信开发者工具或 H5 浏览器
```

### 云函数开发
```bash
# 云函数位于 uniCloud-aliyun/cloudfunctions/
# 每个函数都有自己的 package.json 和依赖
# 通过 HBuilderX 部署：右键函数 -> 上传并运行
```

### 测试
- **推荐测试方式**：直接在主页面（pages/watermark-remover/index.vue）输入对应平台链接测试
- **云函数测试**：在HBuilderX中右键云函数选择"本地运行"进行单独测试
- **调试模式**：通过统一解析器的debug选项查看详细解析过程
- **注意**：不需要创建单独的测试页面，避免pages.json配置冗余

## 平台特性

### 支持的平台
- **抖音**: 视频链接解析和内容提取
- **小红书**: 图片和视频内容，支持 Live Photo
- **快手**: 视频内容解析
- **B站**: 动态、专栏、视频内容解析，支持 Live Photo
- **本地文件**: 图片和视频处理

### 云函数架构

#### 统一解析器架构 (v1.3.0+)
- **`unified-parser`** - 统一入口，自动平台检测和路由分发
- **平台解析器独立** - 每个平台单独的解析云函数，互不影响
- **配置化管理** - 新平台只需在统一解析器中添加配置
- **标准化接口** - 所有解析器返回统一的数据格式

#### 解析流程
1. 前端调用 `unified-parser` 统一接口
2. 自动检测平台类型（抖音、小红书、快手、B站）
3. 路由到对应的专用解析器
4. 返回标准化的解析结果
5. 前端结果页面统一展示

#### 扩展能力
- **新平台接入**：只需添加解析器 + 配置路由
- **前端零修改**：新平台自动被前端支持
- **独立维护**：各平台解析器可独立更新

## 重要说明

### uniCloud 集成
- 项目使用阿里云版本的 uniCloud
- 数据库 schema 位于 `uniCloud-aliyun/database/`
- 通过 uni-id 系统进行身份认证
- 微信小程序安全网络初始化

### 多端兼容性
- Vue 3 + uni-app 框架
- 支持微信小程序、H5 和原生 App
- 结果页面使用自定义导航样式
- 平台特定条件编译（#ifdef）

### 内容处理流程
1. 链接输入验证和解析
2. 平台检测（抖音、小红书等）
3. 云函数处理
4. 内容提取和去水印
5. 结果展示和下载选项
6. 历史记录保存（可选，用户控制）

## 开发环境要求
- 需要 HBuilderX 3.0+
- 微信开发者工具用于小程序测试
- uniCloud 控制台用于云函数管理
- Vue 3 + uni-app 框架